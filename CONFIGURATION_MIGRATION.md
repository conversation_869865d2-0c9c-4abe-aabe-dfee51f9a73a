# Configuration Migration Summary

This document summarizes the migration of Backstage configuration from `app-config.local.yaml` to `app-config.yaml` with environment variable support.

## Changes Made

### 1. Configuration Consolidation
- All configuration from `app-config.local.yaml` has been merged into `app-config.yaml`
- The main configuration file (`app-config.yaml`) now contains the complete application setup
- `app-config.local.yaml` can now be safely removed (it's already ignored by git)

### 2. Environment Variable Migration
The following sensitive values have been moved to environment variables:

| Configuration | Old Value | New Environment Variable |
|---------------|-----------|-------------------------|
| Database password | `secret` | `${DB_PASSWORD}` |
| GitHub OAuth Client ID | `********************` | `${GITHUB_CLIENT_ID}` |
| GitHub OAuth Client Secret | `eb00182ef12e241736d9081c5e36d9cd2fe1a5d3` | `${GITHUB_CLIENT_SECRET}` |
| GitHub Token | Already using `${GITHUB_TOKEN}` | No change |

### 3. Environment File Creation
- Created `.env` file in the project root with actual sensitive values
- The `.env` file is already properly ignored by git (listed in `.gitignore`)

## Usage Instructions

### Local Development
1. Ensure the `.env` file contains the correct values for your environment
2. Update the `GITHUB_TOKEN` value in `.env` with your actual GitHub Personal Access Token
3. Start the application normally with `yarn start`

### Production Deployment
1. Set the environment variables in your deployment environment:
   ```bash
   export DB_PASSWORD="your_production_db_password"
   export GITHUB_TOKEN="your_github_token"
   export GITHUB_CLIENT_ID="your_github_oauth_client_id"
   export GITHUB_CLIENT_SECRET="your_github_oauth_client_secret"
   ```
2. The application will automatically use these environment variables

### Environment Variables Reference
- `DB_PASSWORD`: PostgreSQL database password
- `GITHUB_TOKEN`: GitHub Personal Access Token for repository integration
- `GITHUB_CLIENT_ID`: GitHub OAuth application client ID
- `GITHUB_CLIENT_SECRET`: GitHub OAuth application client secret
- `BACKEND_SECRET`: (Optional) Backend authentication secret
- `GHE_TOKEN`: (Optional) GitHub Enterprise token

## Security Benefits
- Sensitive values are no longer stored in configuration files
- Configuration files can be safely committed to version control
- Environment-specific values can be managed separately
- Reduced risk of accidentally committing secrets

## Next Steps
1. Remove `app-config.local.yaml` if no longer needed
2. Update deployment scripts to set environment variables
3. Update team documentation with new environment variable requirements
4. Consider using a secrets management system for production deployments

## Verification
The configuration has been tested and loads successfully. You can verify the setup by running:
```bash
yarn backstage-cli config:check --config app-config.yaml
```
